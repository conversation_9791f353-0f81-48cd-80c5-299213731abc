# Doubly Circular Linked List Employee Attendance System

A C++ implementation of employee attendance management using doubly circular linked list data structure.

## 📋 Project Overview

This project implements a **doubly circular linked list** for employee attendance management. The system enforces unique employee IDs (primary key constraint) and provides comprehensive CRUD operations with real-time attendance tracking.

### ✨ Key Features
- ✅ **Unique Employee IDs** - Primary key constraint prevents duplicates
- ✅ **Real-time Attendance** - Check-in/check-out with timestamps
- ✅ **Complete CRUD Operations** - Create, Read, Update, Delete
- ✅ **Sorting Algorithm** - Bubble sort by employee ID
- ✅ **Memory Management** - Proper allocation and cleanup
- ✅ **Error Handling** - Comprehensive input validation

## 🚀 Quick Start

### Prerequisites
- C++ compiler (g++, clang++, or Visual Studio)
- C++11 or later standard

### Compilation & Execution
```bash
# Compile
g++ -std=c++11 main.cpp -o attendance_system

# Run
./attendance_system
```

### Sample Usage
```
=== Employee Attendance Status ===
ID    Name                 Status     Check-in                     Check-out
--------------------------------------------------------------------------------
101   <PERSON>           Present    Mon Dec 11 09:15:30 2023     -
102   Sarah <PERSON>        Absent     -                            Mon Dec 11 17:30:45 2023
103   <PERSON> Davis           Present    Mon Dec 11 08:45:20 2023     -
104   Emily Brown          Present    Mon Dec 11 09:00:15 2023     -
105   David Wilson         Absent     -                            -
106   Lisa Anderson        Present    Mon Dec 11 08:30:10 2023     -
--------------------------------------------------------------------------------
```

## 🏗️ Data Structure

**Doubly Circular Linked List** with the following properties:
- **Bidirectional traversal** (next/prev pointers)
- **Circular connections** (no NULL endpoints)
- **Dynamic size** (grows/shrinks at runtime)
- **Primary key constraint** (unique employee IDs)

## 📖 Menu Options

1. **Add Employee** - Register new employee with unique ID validation
2. **Check-in Employee** - Record arrival time with timestamp
3. **Check-out Employee** - Record departure time with timestamp
4. **Search Employee** - Find employee by ID
5. **Display All Employees** - Show formatted attendance table
6. **Update Employee Name** - Modify employee information
7. **Delete Employee** - Remove employee from system
8. **Sort Employees by ID** - Arrange by ascending ID order
9. **Exit** - Terminate program

## 🧪 Sample Test Data

Example employee data for testing:

```cpp
// Sample employee data for testing
ID: 101, Name: "John Smith"
ID: 102, Name: "Sarah Johnson"
ID: 103, Name: "Mike Davis"
ID: 104, Name: "Emily Brown"
ID: 105, Name: "David Wilson"
ID: 106, Name: "Lisa Anderson"
```

### Test Scenarios
1. **Primary Key Test**: Try adding duplicate ID (should fail)
2. **Attendance Flow**: Add → Check-in → Check-out → Display
3. **CRUD Operations**: Create, Read, Update, Delete employees
4. **Sorting**: Add in random order, then sort by ID
5. **Edge Cases**: Empty list, single employee, boundary conditions

## ⚡ Performance Overview

| Operation | Time Complexity | Space Complexity |
|-----------|----------------|------------------|
| Add Employee | O(n) | O(1) |
| Search | O(n) | O(1) |
| Check-in/out | O(n) | O(1) |
| Delete | O(n) | O(1) |
| Display All | O(n) | O(1) |
| Sort | O(n²) | O(1) |

## 📚 Documentation

For detailed technical documentation, implementation details, and comprehensive analysis, see:
- **[DOCUMENTATION.md](DOCUMENTATION.md)** - Complete technical documentation
- **[main.cpp](main.cpp)** - Source code with detailed comments

## 🔧 How It Works

### Data Structure Implementation
The system uses a **doubly circular linked list** where:
- Each employee node contains data and two pointers (next/prev)
- The last node points back to the first node (circular)
- The first node's previous pointer points to the last node
- No NULL pointers exist in a non-empty list

### Primary Key Constraint
- Employee IDs must be unique across the system
- Before adding a new employee, the system searches for existing IDs
- Duplicate IDs are rejected with an error message
- Ensures data integrity and prevents conflicts

### Memory Management
- Dynamic memory allocation using `new` operator
- Proper cleanup in destructor using `delete`
- Circular references maintained during all operations
- No memory leaks through proper deallocation

### Algorithm Efficiency
- **Search Operations**: Linear search through circular list
- **Sorting**: Bubble sort algorithm for simplicity
- **Insertion/Deletion**: Maintains circular structure integrity
- **Display**: Single traversal with formatted output

## 📄 License

Educational use - Data Structures and Algorithms Implementation

