# Group 2 - Doubly Circular Linked List Employee Attendance System

## Group Members
1. <PERSON> Yehualashet [IHRCS-352438-16]
2. <PERSON> [IHRCS-9781-16]
3. <PERSON><PERSON><PERSON> [IHRCS-328669-16]
4. <PERSON><PERSON><PERSON><PERSON> [IHRCS-9599-16]
5. <PERSON><PERSON><PERSON> [IHRCS-829949-16]
6. <PERSON><PERSON><PERSON><PERSON> [IHRCS-907359-16]

## Project Description
This project implements a **doubly-circular linked list** to handle employee attendance management for an organization. The system enforces **unique employee IDs (Primary Key constraint)** and supports comprehensive attendance tracking with check-in/check-out operations along with full CRUD functionality. The implementation demonstrates core data structure concepts with proper time complexity analysis.

## Current Status ✅
- ✅ **Primary Key Constraint**: Employee IDs are now unique (no duplicates allowed)
- ✅ **Complete CRUD Operations**: Create, Read, Update, Delete functionality
- ✅ **Attendance Tracking**: Check-in/Check-out with timestamps
- ✅ **Sorting Algorithm**: Bubble sort implementation
- ✅ **Memory Management**: Proper cleanup and destructor
- ✅ **Error Handling**: Comprehensive validation and error messages
- ✅ **Menu-driven Interface**: User-friendly console application

## Features
- **Add Employee**: Register new employees with unique ID validation
- **Check-in**: Record employee arrival time with timestamp
- **Check-out**: Record employee departure time with timestamp
- **Search**: Find employee by ID with O(n) complexity
- **Display All**: Show all employees in formatted table
- **Update**: Modify employee information
- **Delete**: Remove employee from system with proper linking
- **Sort**: Arrange employees by ID using bubble sort algorithm

## Data Structure: Doubly Circular Linked List

### Structure Definition
```cpp
struct Employee {
    int id;                    // Primary Key (Unique)
    string name;              // Employee name
    string checkInTime;       // Timestamp for check-in
    string checkOutTime;      // Timestamp for check-out
    bool isPresent;           // Current attendance status
    Employee *next;           // Pointer to next node
    Employee *prev;           // Pointer to previous node
};
```

### Properties
- **Doubly Linked**: Each node has pointers to both next and previous nodes
- **Circular**: Last node points back to first node, first node's previous points to last node
- **Primary Key**: Employee ID must be unique across all nodes
- **Dynamic**: Size can grow/shrink during runtime

## Time Complexity Analysis

| Operation | Best Case | Average Case | Worst Case | Space Complexity |
|-----------|-----------|--------------|------------|------------------|
| **Add Employee** | O(1) | O(n) | O(n) | O(1) |
| **Search Employee** | O(1) | O(n) | O(n) | O(1) |
| **Check-in/Check-out** | O(1) | O(n) | O(n) | O(1) |
| **Update Employee** | O(1) | O(n) | O(n) | O(1) |
| **Delete Employee** | O(1) | O(n) | O(n) | O(1) |
| **Display All** | O(n) | O(n) | O(n) | O(1) |
| **Sort (Bubble Sort)** | O(n) | O(n²) | O(n²) | O(1) |

### Complexity Explanations
- **Add Employee**: O(n) due to ID uniqueness check via search operation
- **Search Operations**: O(n) linear search through circular list
- **Sort**: O(n²) bubble sort with O(1) space (in-place sorting)
- **Display**: O(n) must traverse entire list once

## Sample Data and Test Cases

### Sample Employee Data
```
ID: 101, Name: "John Smith"
ID: 102, Name: "Sarah Johnson"
ID: 103, Name: "Mike Davis"
ID: 104, Name: "Emily Brown"
ID: 105, Name: "David Wilson"
```

### Test Scenarios
1. **Primary Key Test**: Try adding employee with duplicate ID (should fail)
2. **Check-in/Check-out Flow**: Add employee → Check-in → Check-out
3. **Search Test**: Search for existing and non-existing employees
4. **Update Test**: Modify employee name
5. **Delete Test**: Remove employee and verify list integrity
6. **Sort Test**: Add employees in random order, then sort by ID

### Expected Output Format
```
=== Employee Attendance Status ===
ID    Name                 Status     Check-in                     Check-out
--------------------------------------------------------------------------------
101   <USER> <GROUP>           Present    Mon Dec 11 14:30:25 2023     -
102   Sarah Johnson        Absent     -                            Mon Dec 11 17:45:10 2023
103   Mike Davis           Present    Mon Dec 11 09:15:30 2023     -
--------------------------------------------------------------------------------
```

## How to Compile and Run

### Prerequisites
- C++ Compiler (g++, clang++, or Visual Studio)
- C++11 or later standard

### Compilation Commands
```bash
# Using g++
g++ -std=c++11 main.cpp -o attendance_system

# Using clang++
clang++ -std=c++11 main.cpp -o attendance_system

# For Windows (MinGW)
g++ main.cpp -o attendance_system.exe
```

### Running the Program
```bash
# Linux/Mac
./attendance_system

# Windows
attendance_system.exe
```

## Menu Options
1. **Add Employee** - Register new employee with unique ID validation
2. **Check-in Employee** - Record arrival time for existing employee
3. **Check-out Employee** - Record departure time for checked-in employee
4. **Search Employee** - Find and display employee information by ID
5. **Display All Employees** - Show formatted table of all employees
6. **Update Employee Name** - Modify existing employee's name
7. **Delete Employee** - Remove employee from system
8. **Sort Employees by ID** - Arrange employees in ascending order by ID
9. **Exit** - Terminate the program

## Implementation Details

### Core Features
- **Primary Key Constraint**: Enforced unique employee IDs with validation
- **Automatic Timestamps**: Real-time check-in/check-out recording using system time
- **Circular List Management**: Proper handling of circular references in all operations
- **Memory Management**: Dynamic allocation with proper cleanup in destructor
- **Error Handling**: Comprehensive validation with user-friendly error messages
- **Table Formatting**: Aligned columns with consistent spacing for readability

### Data Structure Advantages
- **Bidirectional Traversal**: Can move forward and backward through the list
- **Efficient Insertion/Deletion**: O(1) when position is known
- **Circular Nature**: No NULL pointers, always connected
- **Dynamic Size**: Grows and shrinks as needed

### Algorithm Implementations
- **Bubble Sort**: Simple O(n²) sorting algorithm for educational purposes
- **Linear Search**: Sequential search through circular list
- **Circular List Operations**: Proper handling of head/tail connections

## Code Structure

### Main Components
```cpp
struct Employee {
    // Employee data and pointers
};

class AttendanceSystem {
private:
    Employee *head;           // Pointer to first employee
    int totalEmployees;       // Count of employees

public:
    // CRUD Operations
    void addEmployee(int id, string name);
    Employee* searchEmployee(int id);
    void updateEmployee(int id, string newName);
    void deleteEmployee(int id);

    // Attendance Operations
    void checkIn(int id);
    void checkOut(int id);

    // Utility Operations
    void displayAll();
    void sortByID();
    string getCurrentTime();
};
```

### Key Methods
- **addEmployee()**: Validates ID uniqueness before insertion
- **searchEmployee()**: Linear search with circular list traversal
- **deleteEmployee()**: Maintains circular links after removal
- **sortByID()**: In-place bubble sort implementation
- **displayAll()**: Formatted output with proper alignment

## Learning Objectives Achieved

### Data Structure Concepts
- ✅ **Doubly Linked Lists**: Implementation with prev/next pointers
- ✅ **Circular Lists**: Handling circular references and traversal
- ✅ **Dynamic Memory**: Allocation and deallocation
- ✅ **Pointer Manipulation**: Complex pointer operations

### Algorithm Concepts
- ✅ **Searching**: Linear search implementation
- ✅ **Sorting**: Bubble sort algorithm
- ✅ **Insertion/Deletion**: Maintaining list integrity
- ✅ **Traversal**: Circular list navigation

### Software Engineering Concepts
- ✅ **Data Validation**: Primary key constraints
- ✅ **Error Handling**: Comprehensive validation
- ✅ **User Interface**: Menu-driven design
- ✅ **Code Organization**: Structured programming

## Testing and Validation

### Unit Test Cases
1. **Empty List Operations**: Test operations on empty list
2. **Single Node Operations**: Test with one employee
3. **Multiple Node Operations**: Test with several employees
4. **Boundary Conditions**: Test edge cases
5. **Error Conditions**: Test invalid inputs

### Integration Testing
- Complete workflow testing (Add → Check-in → Check-out → Delete)
- Data persistence throughout operations
- Memory leak detection
- Performance testing with large datasets

## Future Enhancements

### Possible Improvements
- **File I/O**: Save/load employee data from files
- **Advanced Sorting**: Implement quicksort or mergesort
- **Hash Table**: O(1) search using hash table for IDs
- **Date/Time Validation**: More sophisticated time handling
- **Reporting**: Generate attendance reports
- **GUI Interface**: Graphical user interface

### Optimization Opportunities
- **Search Optimization**: Implement binary search (requires sorted list)
- **Memory Pool**: Pre-allocate memory for better performance
- **Caching**: Cache frequently accessed employees

## Conclusion

This project successfully demonstrates the implementation of a doubly circular linked list with practical applications in employee attendance management. The system enforces data integrity through primary key constraints while providing comprehensive CRUD operations with proper time complexity analysis. The implementation serves as an excellent educational tool for understanding advanced linked list concepts and their real-world applications.

## Repository Information
- **Language**: C++
- **Standard**: C++11 or later
- **Platform**: Cross-platform (Windows, Linux, macOS)
- **Dependencies**: Standard C++ libraries only
- **License**: Educational use
