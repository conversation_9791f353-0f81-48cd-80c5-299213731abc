{"tasks": [{"type": "cppbuild", "label": "C/C++: g++ build active file", "command": "g++", "args": ["-fdiagnostics-color=always", "-g", "${file}", "-o", "${fileDirname}/${fileBasenameNoExtension}.exe"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": true}, "detail": "Task generated by <PERSON>bugger."}, {"type": "shell", "label": "Build and Run C++", "command": "g++", "args": ["${file}", "-o", "${fileDirname}/${fileBasenameNoExtension}.exe"], "group": {"kind": "build", "isDefault": false}, "options": {"cwd": "${fileDirname}"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$gcc"]}, {"type": "cppbuild", "label": "C/C++: g++.exe build active file", "command": "C:\\Program Files (x86)\\Falcon\\MinGW\\bin\\g++.exe", "args": ["-fdiagnostics-color=always", "-g", "${file}", "-o", "${fileDirname}\\${fileBasenameNoExtension}.exe"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$gcc"], "group": "build", "detail": "Task generated by <PERSON>bugger."}], "version": "2.0.0"}